package tablet_update

import (
	"marketing-app/internal/handler/tablet_update"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/repository"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
)

type TabletUpdateRouter interface {
	Register(r *gin.RouterGroup)
}

type tabletUpdateRouter struct{}

func NewTabletUpdateRouter() TabletUpdateRouter {
	return &tabletUpdateRouter{}
}

func (tur *tabletUpdateRouter) Register(r *gin.RouterGroup) {
	// 获取数据库连接
	database, _ := db.GetDB()

	// 初始化各层组件
	tabletUpdateRepository := repository.NewTabletUpdateRepository(database)
	tabletUpdateService := service.NewTabletUpdateService(tabletUpdateRepository)
	tabletUpdateController := tablet_update.NewTabletUpdateController(tabletUpdateService)

	// 注册路由
	r.GET("/:params", tabletUpdateController.Detail) // 获取平板更新详情
}
