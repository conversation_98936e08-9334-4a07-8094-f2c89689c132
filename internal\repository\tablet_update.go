package repository

import (
	"marketing-app/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TabletUpdateRepository interface {
	// GetTabletUpdatesByIDs 根据ID列表获取平板更新记录
	GetTabletUpdatesByIDs(c *gin.Context, ids []uint) ([]model.AppNotificationTabletUpdate, error)
}

type tabletUpdateRepository struct {
	db *gorm.DB
}

func NewTabletUpdateRepository(db *gorm.DB) TabletUpdateRepository {
	return &tabletUpdateRepository{
		db: db,
	}
}

// GetTabletUpdatesByIDs 根据ID列表获取平板更新记录
func (r *tabletUpdateRepository) GetTabletUpdatesByIDs(c *gin.Context, ids []uint) ([]model.AppNotificationTabletUpdate, error) {
	var updates []model.AppNotificationTabletUpdate

	err := r.db.WithContext(c).
		Where("id IN ?", ids).
		Order("type").
		Order("id").
		Find(&updates).Error

	return updates, err
}
