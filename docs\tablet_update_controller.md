# TabletUpdateController 文档

## 概述

TabletUpdateController 是一个新增的控制器，用于处理平板更新通知的详情展示。它实现了与PHP版本相同的逻辑，支持课程上新和软件更新两种类型的通知。

## 功能特性

- **参数解密**: 支持DES解密加密的参数
- **参数验证**: 验证十六进制字符串和时间格式
- **数据处理**: 处理JSON数据，去除多余空格和换行符
- **分类展示**: 根据类型（课程/应用）进行不同的数据组织
- **HTML渲染**: 返回格式化的HTML页面

## API 接口

### 获取平板更新详情

**请求方式**: `GET`

**请求路径**: `/notification/tablet_update/view/{params}`

**参数说明**:
- `params` (路径参数): DES加密的ID列表，格式为逗号分隔的数字（如："1,2,3"）
- `time` (查询参数): 时间参数，格式为 "2006-01-02 15:04:05"

**响应格式**: HTML页面

**示例请求**:
```
GET /notification/tablet_update/view/a1b2c3d4e5f6?time=2023-12-01 10:00:00
```

## 实现架构

### 1. 模型层 (Model)
- `AppNotificationTabletUpdate`: 平板更新通知表模型

### 2. 数据访问层 (Repository)
- `TabletUpdateRepository`: 提供数据库访问接口
- `GetTabletUpdatesByIDs`: 根据ID列表获取更新记录

### 3. 业务逻辑层 (Service)
- `TabletUpdateService`: 处理业务逻辑
- `GetTabletUpdateDetail`: 解密参数、验证数据、组织响应

### 4. 控制器层 (Handler)
- `TabletUpdateController`: 处理HTTP请求
- `Detail`: 处理详情请求并返回HTML视图

### 5. 路由层 (Router)
- `TabletUpdateRouter`: 注册路由
- 路径: `/notification/tablet_update/view/{params}`

## 工具类

### DES加密解密工具
位置: `internal/pkg/utils/des.go`

**主要功能**:
- `NewDESCrypto(key, iv string)`: 创建DES实例
- `Encrypt(plaintext string)`: 加密并返回十六进制字符串
- `Decrypt(hexStr string)`: 解密十六进制字符串
- `IsHexString(s string)`: 验证十六进制字符串

**使用示例**:
```go
des := utils.NewDESCrypto("your_key", "your_iv")
encrypted, err := des.Encrypt("1,2,3")
decrypted, err := des.Decrypt(encrypted)
```

## 配置说明

### DES密钥配置
当前使用默认密钥，生产环境需要配置实际的密钥：

```go
// 在 internal/pkg/utils/des.go 中修改
func GetDESFromEnv() *DESCrypto {
    key := config.GetString("des.key")  // 从配置获取
    iv := config.GetString("des.iv")    // 从配置获取
    return NewDESCrypto(key, iv)
}
```

### 模板配置
HTML模板位置: `templates/`
- `tablet_update_view.html`: 主要展示模板
- `error.html`: 错误页面模板

## 数据格式

### 课程类型数据
```json
{
  "title": "课程上新(2)",
  "type": "course",
  "time": "2023-12-01 10:00:00",
  "rows": {
    "数学": [
      {
        "title": "小学数学基础",
        "description": "一年级数学课程",
        "subject": "数学",
        "grade": "一年级"
      }
    ]
  }
}
```

### 应用类型数据
```json
{
  "title": "读书郎软件更新(1)",
  "type": "app",
  "time": "2023-12-01 10:00:00",
  "data": [
    {
      "title": "读书郎学习助手",
      "description": "修复已知问题，提升用户体验",
      "version": "v2.1.0",
      "size": "25.6MB"
    }
  ]
}
```

## 错误处理

### 常见错误
1. **参数错误**: 非十六进制字符串
2. **时间格式错误**: 不符合 "2006-01-02 15:04:05" 格式
3. **参数解密失败**: DES解密失败
4. **参数格式错误**: 解密后不是有效的ID列表格式
5. **数据不存在**: 数据库中没有对应的记录

### 错误响应
所有错误都会返回HTML错误页面，显示具体的错误信息。

## 测试

### 单元测试
位置: `internal/service/tablet_update_test.go`

**运行测试**:
```bash
go test ./internal/service -v
```

### 集成测试
需要配置实际的数据库和DES密钥进行完整的集成测试。

## 部署注意事项

1. **配置DES密钥**: 确保生产环境配置了正确的DES密钥和IV
2. **模板文件**: 确保 `templates/` 目录在部署包中
3. **数据库表**: 确保 `app_notification_tablet_update` 表存在
4. **权限设置**: 该接口为公开接口，无需认证

## 与PHP版本的对应关系

| PHP方法/功能 | Go实现 | 说明 |
|-------------|--------|------|
| `ctype_xdigit()` | `utils.IsHexString()` | 验证十六进制字符串 |
| `DES::decrypt()` | `DESCrypto.Decrypt()` | DES解密 |
| `v::regex()` | `regexp.MatchString()` | 正则表达式验证 |
| `DB::table()->whereIn()` | `GetTabletUpdatesByIDs()` | 数据库查询 |
| `json_decode()` | `json.Unmarshal()` | JSON解析 |
| `view()` | `c.HTML()` | 视图渲染 |

## 后续优化建议

1. **缓存机制**: 对频繁访问的数据添加缓存
2. **日志记录**: 添加详细的操作日志
3. **监控指标**: 添加性能监控和错误统计
4. **安全加固**: 添加访问频率限制和参数校验
5. **国际化**: 支持多语言错误信息
