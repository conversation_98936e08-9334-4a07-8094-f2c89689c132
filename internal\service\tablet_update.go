package service

import (
	"encoding/json"
	"fmt"
	"marketing-app/internal/handler/tablet_update/dto"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/repository"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type TabletUpdateService interface {
	// GetTabletUpdateDetail 获取平板更新详情
	GetTabletUpdateDetail(c *gin.Context, req *dto.TabletUpdateDetailRequest) (*dto.TabletUpdateDetailResponse, error)
}

type tabletUpdateService struct {
	tabletUpdateRepo repository.TabletUpdateRepository
}

func NewTabletUpdateService(tabletUpdateRepo repository.TabletUpdateRepository) TabletUpdateService {
	return &tabletUpdateService{
		tabletUpdateRepo: tabletUpdateRepo,
	}
}

// GetTabletUpdateDetail 获取平板更新详情
func (s *tabletUpdateService) GetTabletUpdateDetail(c *gin.Context, req *dto.TabletUpdateDetailRequest) (*dto.TabletUpdateDetailResponse, error) {
	// 验证参数是否为十六进制
	if !utils.IsHexString(req.Params) {
		return nil, errors.New("参数错误")
	}

	// 验证时间格式
	if _, err := time.Parse("2006-01-02 15:04:05", req.Time); err != nil {
		return nil, errors.New("时间格式错误")
	}

	// 解密参数
	des := utils.GetDESFromEnv() // 这里需要从配置获取实际的key和iv
	decryptedParams, err := des.Decrypt(req.Params)
	if err != nil {
		return nil, errors.New("参数解密失败")
	}

	// 验证解密后的参数格式（应该是逗号分隔的数字）
	matched, err := regexp.MatchString(`^[1-9]\d*(,[1-9]\d*)*$`, decryptedParams)
	if err != nil || !matched {
		return nil, errors.New("参数格式错误")
	}

	// 解析ID列表
	idStrings := strings.Split(decryptedParams, ",")
	ids := make([]uint, 0, len(idStrings))
	for _, idStr := range idStrings {
		id, err := strconv.ParseUint(strings.TrimSpace(idStr), 10, 32)
		if err != nil {
			return nil, errors.New("参数格式错误")
		}
		ids = append(ids, uint(id))
	}

	// 从数据库获取数据
	data, err := s.tabletUpdateRepo.GetTabletUpdatesByIDs(c, ids)
	if err != nil {
		return nil, err
	}

	if len(data) == 0 {
		return nil, errors.New("数据不存在")
	}

	// 处理数据
	var items []dto.TabletUpdateItem
	for _, item := range data {
		// 处理JSON数据：去除多余空格和换行符
		cleanData := regexp.MustCompile(`[ \t]+`).ReplaceAllString(item.Data, "")
		cleanData = regexp.MustCompile(`(\r\n)|\r|\n`).ReplaceAllString(cleanData, "<br/>")

		// 解析JSON
		var jsonData map[string]interface{}
		if err := json.Unmarshal([]byte(cleanData), &jsonData); err != nil {
			log.Error("json解析有误：", map[string]interface{}{
				"id":   item.ID,
				"type": item.Type,
				"data": item.Data,
			})
			continue
		}

		items = append(items, dto.TabletUpdateItem{
			ID:   item.ID,
			Type: item.Type,
			Data: jsonData,
		})
	}

	if len(items) == 0 {
		return nil, errors.New("数据不存在")
	}

	// 确定标题和类型
	title := "课程上新"
	updateType := items[0].Type
	if updateType == "app" {
		title = "读书郎软件更新"
	}
	title += fmt.Sprintf("(%d)", len(items))

	// 构建响应
	response := &dto.TabletUpdateDetailResponse{
		Title: title,
		Type:  updateType,
		Time:  req.Time,
	}

	if updateType != "app" {
		// 课程类型：按type分组
		rows := make(map[string][]map[string]interface{})
		for _, item := range items {
			if rows[item.Type] == nil {
				rows[item.Type] = make([]map[string]interface{}, 0)
			}
			rows[item.Type] = append(rows[item.Type], item.Data)
		}
		response.Rows = rows
	} else {
		// 应用类型：直接返回数据数组
		data := make([]map[string]interface{}, 0, len(items))
		for _, item := range items {
			data = append(data, item.Data)
		}
		response.Data = data
	}

	return response, nil
}
