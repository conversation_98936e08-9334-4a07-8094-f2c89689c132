package tablet_update

import (
	"marketing-app/internal/handler/tablet_update/dto"
	"marketing-app/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type TabletUpdateController interface {
	// Detail 获取平板更新详情
	Detail(c *gin.Context)
}

type tabletUpdateController struct {
	service service.TabletUpdateService
}

func NewTabletUpdateController(service service.TabletUpdateService) TabletUpdateController {
	return &tabletUpdateController{
		service: service,
	}
}

// Detail 获取平板更新详情
// @Summary 获取平板更新详情
// @Description 根据加密参数获取平板更新详情，支持课程上新和软件更新
// @Tags TabletUpdate
// @Accept json
// @Produce json
// @Param params path string true "加密的ID列表参数"
// @Param time query string true "时间参数，格式：2006-01-02 15:04:05"
// @Success 200 {object} dto.TabletUpdateDetailResponse
// @Failure 400 {object} handler.ErrorResponse
// @Failure 500 {object} handler.ErrorResponse
// @Router /tablet-update/{params} [get]
func (h *tabletUpdateController) Detail(c *gin.Context) {
	var (
		req  dto.TabletUpdateDetailRequest
		err  error
		resp *dto.TabletUpdateDetailResponse
	)
	defer func() {
		if err != nil {
			// 如果出错，返回HTML错误页面
			c.HTML(http.StatusBadRequest, "error.html", gin.H{
				"error": err.Error(),
			})
		} else {
			// 成功时返回HTML视图
			c.HTML(http.StatusOK, "tablet_update_view.html", resp)
		}
	}()

	// 绑定URI参数
	if err = c.ShouldBindUri(&req); err != nil {
		err = errors.Wrap(err, "绑定URI参数失败")
		return
	}

	// 绑定查询参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "绑定查询参数失败")
		return
	}

	// 调用服务层获取详情
	resp, err = h.service.GetTabletUpdateDetail(c, &req)
	if err != nil {
		return
	}
}
