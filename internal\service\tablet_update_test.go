package service

import (
	"context"
	"testing"
	"marketing-app/internal/handler/tablet_update/dto"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockTabletUpdateRepository 模拟的Repository
type MockTabletUpdateRepository struct {
	mock.Mock
}

func (m *MockTabletUpdateRepository) GetTabletUpdatesByIDs(c *gin.Context, ids []uint) ([]model.AppNotificationTabletUpdate, error) {
	args := m.Called(c, ids)
	return args.Get(0).([]model.AppNotificationTabletUpdate), args.Error(1)
}

func TestTabletUpdateService_GetTabletUpdateDetail(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建模拟的repository
	mockRepo := new(MockTabletUpdateRepository)
	service := NewTabletUpdateService(mockRepo)

	// 创建测试上下文
	c, _ := gin.CreateTestContext(nil)

	// 测试数据
	testData := []model.AppNotificationTabletUpdate{
		{
			ID:   1,
			Type: "course",
			Data: `{"title": "数学课程", "description": "小学数学基础课程", "subject": "数学", "grade": "一年级"}`,
		},
		{
			ID:   2,
			Type: "course",
			Data: `{"title": "语文课程", "description": "小学语文基础课程", "subject": "语文", "grade": "一年级"}`,
		},
	}

	// 设置mock期望
	mockRepo.On("GetTabletUpdatesByIDs", c, []uint{1, 2}).Return(testData, nil)

	// 创建请求
	req := &dto.TabletUpdateDetailRequest{
		Params: "test_encrypted_params", // 这里需要实际的加密参数
		Time:   "2023-12-01 10:00:00",
	}

	// 注意：由于我们使用了实际的DES解密，这个测试需要有效的加密参数
	// 在实际测试中，你可能需要：
	// 1. 使用真实的DES密钥加密测试数据
	// 2. 或者修改service以支持依赖注入DES实例进行测试

	// 这里我们跳过实际的测试执行，因为需要有效的加密参数
	t.Skip("需要有效的DES加密参数才能运行此测试")

	// 执行测试
	// result, err := service.GetTabletUpdateDetail(c, req)

	// 验证结果
	// assert.NoError(t, err)
	// assert.NotNil(t, result)
	// assert.Equal(t, "课程上新(2)", result.Title)
	// assert.Equal(t, "course", result.Type)
	// assert.NotNil(t, result.Rows)

	// 验证mock调用
	mockRepo.AssertExpectations(t)
}

func TestDESCrypto(t *testing.T) {
	// 测试DES加密解密功能
	des := utils.NewDESCrypto("testkey8", "testiv12") // 8字节key，8字节iv

	// 测试数据
	plaintext := "1,2,3"

	// 加密
	encrypted, err := des.Encrypt(plaintext)
	assert.NoError(t, err)
	assert.NotEmpty(t, encrypted)

	// 解密
	decrypted, err := des.Decrypt(encrypted)
	assert.NoError(t, err)
	assert.Equal(t, plaintext, decrypted)
}

func TestIsHexString(t *testing.T) {
	// 测试十六进制字符串验证
	assert.True(t, utils.IsHexString("1234567890abcdef"))
	assert.True(t, utils.IsHexString("ABCDEF"))
	assert.False(t, utils.IsHexString("xyz"))
	assert.False(t, utils.IsHexString("123")) // 奇数长度
}
