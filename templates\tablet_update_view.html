<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .title {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }
        .time {
            color: #666;
            font-size: 14px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            color: #444;
            font-size: 18px;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #ddd;
        }
        .item {
            background-color: #f9f9f9;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .item-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .item-content {
            color: #666;
            line-height: 1.6;
        }
        .app-item {
            border-left-color: #28a745;
        }
        .course-item {
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">{{.Title}}</h1>
            <div class="time">更新时间: {{.Time}}</div>
        </div>

        {{if eq .Type "app"}}
            <!-- 应用更新 -->
            {{range .Data}}
            <div class="item app-item">
                <div class="item-title">{{.title}}</div>
                <div class="item-content">{{.description}}</div>
                {{if .version}}<div class="item-content"><strong>版本:</strong> {{.version}}</div>{{end}}
                {{if .size}}<div class="item-content"><strong>大小:</strong> {{.size}}</div>{{end}}
            </div>
            {{end}}
        {{else}}
            <!-- 课程更新 -->
            {{range $courseType, $courses := .Rows}}
            <div class="section">
                <h2 class="section-title">{{$courseType}}</h2>
                {{range $courses}}
                <div class="item course-item">
                    <div class="item-title">{{.title}}</div>
                    <div class="item-content">{{.description}}</div>
                    {{if .subject}}<div class="item-content"><strong>学科:</strong> {{.subject}}</div>{{end}}
                    {{if .grade}}<div class="item-content"><strong>年级:</strong> {{.grade}}</div>{{end}}
                </div>
                {{end}}
            </div>
            {{end}}
        {{end}}
    </div>
</body>
</html>
