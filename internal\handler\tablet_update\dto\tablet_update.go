package dto

import "time"

// TabletUpdateDetailRequest 获取平板更新详情请求
type TabletUpdateDetailRequest struct {
	Params string `uri:"params" binding:"required"` // 加密的参数
	Time   string `form:"time" binding:"required"`  // 时间参数，格式：Y-m-d H:i:s
}

// TabletUpdateItem 平板更新项
type TabletUpdateItem struct {
	ID   uint                   `json:"id"`
	Type string                 `json:"type"`
	Data map[string]interface{} `json:"data"`
}

// TabletUpdateDetailResponse 平板更新详情响应
type TabletUpdateDetailResponse struct {
	Title string                            `json:"title"`
	Type  string                            `json:"type"`
	Time  string                            `json:"time"`
	Rows  map[string][]map[string]interface{} `json:"rows,omitempty"` // 课程类型分组
	Data  []map[string]interface{}          `json:"data,omitempty"` // 应用更新数据
}
